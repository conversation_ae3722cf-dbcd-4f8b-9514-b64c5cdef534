{"auditReportVersion": 2, "vulnerabilities": {"fastify-static": {"name": "fastify-static", "severity": "low", "isDirect": false, "via": [{"source": "RN8J5kexW31NMzkqUc2GcYE7zzQLodWLuDL7/0XKWJUiS990IQN4OoxFNF4WFKg1glsWkk9OITNDAxZBpjcVjw==", "name": "fastify-static", "dependency": "fastify-static", "title": "Depends on vulnerable versions of send", "url": null, "severity": "low", "cwe": ["CWE-79"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L"}, "range": "*"}, "send"], "effects": ["fastify-swagger"], "range": "*", "nodes": ["node_modules/fastify-static", "node_modules/fastify-static-deprecated"], "fixAvailable": false}, "fastify-swagger": {"name": "fastify-swagger", "severity": "low", "isDirect": true, "via": [{"source": "1kwTQ/LuEPUgIjf6Uhw8UaPOINSWSdAkiCyS2x9d3DbI56yiSoU1rIXX+a81IcxPSS5kd+vT01Y4EkGHGC5eTw==", "name": "fastify-swagger", "dependency": "fastify-swagger", "title": "Depends on vulnerable versions of fastify-static", "url": null, "severity": "low", "cwe": ["CWE-79"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L"}, "range": "*"}, "fastify-static"], "effects": [], "range": "*", "nodes": ["node_modules/fastify-swagger", "node_modules/fastify-swagger-deprecated"], "fixAvailable": false}, "send": {"name": "send", "severity": "low", "isDirect": false, "via": [{"source": 1100526, "name": "send", "dependency": "send", "title": "send vulnerable to template injection that can lead to XSS", "url": "https://github.com/advisories/GHSA-m6fv-jmcg-4jfg", "severity": "low", "cwe": ["CWE-79"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L"}, "range": "<0.19.0"}], "effects": ["fastify-static"], "range": "<0.19.0", "nodes": ["node_modules/send"], "fixAvailable": false}}, "metadata": {"vulnerabilities": {"info": 0, "low": 3, "moderate": 0, "high": 0, "critical": 0, "total": 3}, "dependencies": {"prod": 367, "dev": 540, "optional": 4, "peer": 4, "peerOptional": 0, "total": 908}}}